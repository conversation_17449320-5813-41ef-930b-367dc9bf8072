##########################################################################
# Node.js image used to serve the application in dev and test environments
##########################################################################
FROM node:22-alpine as builder
ARG NODE_MAX_OLD_SPACE_SIZE=3075

USER root
LABEL version="1.0" maintainer="<EMAIL>"
WORKDIR /var/www/html/app

COPY package.json  .
COPY package-lock.json .
COPY ./.docker/docker-entrypoint.sh /usr/local/bin


RUN npm install;

COPY . .

RUN if [ -d ".angular" ]; then rm -rf .angular; fi
RUN npm run build

RUN chmod +x /usr/local/bin/docker-entrypoint.sh

EXPOSE 4200
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
USER node

########################################################################
# Nginx image used to serve the compiled application in prod environment
########################################################################
FROM nginx:1.15.2-alpine

WORKDIR /usr/share/nginx/html

COPY --from=builder /usr/local/bin/docker-entrypoint.sh /usr/local/bin

RUN rm -rf /usr/share/nginx/html/* && rm -rf /etc/nginx/conf.d/* \
    && chmod +x /usr/local/bin/docker-entrypoint.sh

COPY ./.docker/nginx/site.conf /etc/nginx/conf.d/site.conf
COPY --from=builder --chown=nginx:nginx /var/www/html/app/dist/issue-tracker/browser/ /usr/share/nginx/html/

EXPOSE 80

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
