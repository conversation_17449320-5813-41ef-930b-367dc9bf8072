import { Pipe, PipeTransform } from '@angular/core';
import { intervalToDuration } from 'date-fns';

@Pipe({
    name: 'duration',
    standalone: true,
})
export class DurationPipe implements PipeTransform {
    transform(minutes: number): string {
        if (!minutes || minutes < 0) {
            return '0s';
        }
        const seconds = minutes * 60;
        const duration = intervalToDuration({
            start: new Date(0),
            end: new Date(seconds * 1000),
        });

        if (seconds < 60) {
            return `${duration.seconds}s`;
        } else if (seconds < 3600) {
            return `${duration.minutes}m ${duration.seconds ?? 0}s`;
        } else if (seconds < 86400) {
            return `${duration.hours}h ${duration.minutes ?? 0}m`;
        } else {
            return `${duration.days}d ${duration.hours ?? 0}h`;
        }
    }
}
