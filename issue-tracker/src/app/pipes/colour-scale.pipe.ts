import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'colorScaleAdvanced',
    standalone: true,
})
export class ColorScaleAdvancedPipe implements PipeTransform {
    transform(
        value: number,
        limit: number,
        options?: {
            colors?: { green?: string; yellow?: string; red?: string };
            thresholds?: { green?: number; yellow?: number };
        }
    ): string {
        const colors = {
            green: options?.colors?.green || '#22c55e',
            yellow: options?.colors?.yellow || '#eab308',
            red: options?.colors?.red || '#ef4444',
        };

        const thresholds = {
            green: options?.thresholds?.green || 45,
            yellow: options?.thresholds?.yellow || 65,
        };

        if (!value || !limit || limit === 0) {
            return colors.green;
        }

        const percentage = (value / limit) * 100;

        if (percentage <= thresholds.green) {
            return colors.green;
        } else if (percentage <= thresholds.yellow) {
            return colors.yellow;
        } else {
            return colors.red;
        }
    }
}
