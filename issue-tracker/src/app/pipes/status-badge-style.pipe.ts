import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'statusBadgeStyle'
})
export class StatusBadgeStylePipe implements PipeTransform {
  transform(status: string): { [key: string]: string } {
    switch (status.toLowerCase()) {
      case 'for qa':
        return { backgroundColor: '#1E88E5' };
      case 'to do':
        return { backgroundColor: '#FB8C00' };
      case 'in progress':
        return { backgroundColor: '#E7D48D', color: '#000' };
      case 'code review':
        return { backgroundColor: '#6A1B9A' };
      case 'for testing':
        return { backgroundColor: '#8E24AA' };
      case 'in testing':
        return { backgroundColor: '#AB47BC' };
      case 'done':
        return { backgroundColor: '#2E7D32' };
      default:
        return { backgroundColor: '#9E9E9E' };
    }
  }
}