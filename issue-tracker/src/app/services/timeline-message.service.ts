import { Injectable } from '@angular/core';
import { TimelineEvent } from '../types/timeline-event.interface';

/**
 * Service responsible for generating timeline event messages and formatting.
 * Handles the conversion of raw event data into user-friendly messages with rich formatting.
 */
@Injectable({
    providedIn: 'root',
})
export class TimelineMessageService {
    private personIcon = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-person" viewBox="0 0 16 16">
  <path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6m2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0m4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4m-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10s-3.516.68-4.168 1.332c-.678.678-.83 1.418-.832 1.664z"/>
</svg>`;

    /**
     * Generates a formatted event type text for a timeline event
     */
    generateEventTypeText(event: TimelineEvent): string {
        const {
            event_type,
            actor_name,
            repository,
            pr_id,
            pr_link,
            pr_title,
            branch_name,
        } = event;

        // Use database-provided PR ID
        const prId = pr_id;

        switch (event_type) {
            case 'pullrequest:fulfilled':
                return this.formatMessage(
                    `<strong>${actor_name}</strong> merged pull request ${
                        prId
                            ? `<a href="${pr_link}" title="${pr_title}" target="_blank" class="text-blue-600 hover:text-blue-800 underline">#${prId}</a>`
                            : ''
                    } in <strong>${repository}</strong>`
                );

            case 'pullrequest:comment_updated':
                return this.formatMessage(
                    `<strong>${actor_name}</strong> updated a comment on pull request ${
                        prId
                            ? `<a href="${pr_link}" title="${pr_title}" target="_blank" class="text-blue-600 hover:text-blue-800 underline">#${prId}</a>`
                            : ''
                    } in <strong>${repository}</strong>`
                );

            case 'pullrequest:comment_created':
                return this.formatMessage(
                    `<strong>${actor_name}</strong> commented on pull request ${
                        prId
                            ? `<a href="${pr_link}" title="${pr_title}" target="_blank" class="text-blue-600 hover:text-blue-800 underline">#${prId}</a>`
                            : ''
                    } in <strong>${repository}</strong>`
                );

            case 'pullrequest:updated':
                return this.formatMessage(
                    `<strong>${actor_name}</strong> updated pull request ${
                        prId
                            ? `<a href="${pr_link}" title="${pr_title}" target="_blank" class="text-blue-600 hover:text-blue-800 underline">#${prId}</a>`
                            : ''
                    } in <strong>${repository}</strong>`
                );

            case 'pullrequest:approved':
                return this.formatMessage(
                    `<strong>${actor_name}</strong> approved pull request ${
                        prId
                            ? `<a href="${pr_link}" title="${pr_title}" target="_blank" class="text-blue-600 hover:text-blue-800 underline">#${prId}</a>`
                            : ''
                    } in <strong>${repository}</strong>`
                );

            case 'pullrequest:created':
                return this.formatMessage(
                    `<strong>${actor_name}</strong> created pull request ${
                        prId
                            ? `<a href="${pr_link}" title="${pr_title}" target="_blank" class="text-blue-600 hover:text-blue-800 underline">#${prId}</a>`
                            : ''
                    } in <strong>${repository}</strong>`
                );

            case 'pullrequest:changes_request_created':
                return this.formatMessage(
                    `<strong>${actor_name}</strong> requested changes on pull request ${
                        prId
                            ? `<a href="${pr_link}" title="${pr_title}" target="_blank" class="text-blue-600 hover:text-blue-800 underline">#${prId}</a>`
                            : ''
                    } in <strong>${repository}</strong>`
                );

            case 'repo:push':
                // Check if this is a branch closure
                if (this.isBranchClosure(event)) {
                    return this.formatMessage(
                        `<strong>${actor_name}</strong> closed branch <img src="branch-svgrepo-com.svg" alt="Branch Icon" class="inline w-4 h-4"> <strong>${branch_name}</strong>`
                    );
                }
                return this.formatMessage(
                    `<strong>${actor_name}</strong> committed to branch <img src="branch-svgrepo-com.svg" alt="Branch Icon" class="inline w-4 h-4"> <strong>${branch_name}</strong>`
                );

            case 'jira:issue_created':
                return this.formatMessage(
                    `<strong>${actor_name}</strong> created the issue`
                );

            case 'jira:issue_updated':
                return this.formatMessage(
                    `<strong>${actor_name}</strong> updated the issue`
                );

            case 'comment_created':
                return this.formatMessage(
                    `<strong>${actor_name}</strong> added a comment`
                );

            default:
                // Fallback for unmapped event types
                return this.formatMessage(
                    `<strong>${actor_name}</strong> ${event_type.replace(
                        /[_:]/g,
                        ' '
                    )}`
                );
        }
    }

    /**
     * Generates formatted change messages from raw issue changelog data
     */
    generateChangeMessages(event: TimelineEvent): string[] {
        if (!event.issue_changelog || !Array.isArray(event.issue_changelog)) {
            return [];
        }

        return event.issue_changelog.map((item: any) => {
            const fieldId = item.fieldId || item.field;
            // const actor = this.formatActor(event.actor_name);

            switch (fieldId) {
                case 'status':
                    const fromStatus = this.formatStatusValue(
                        item.fromString || 'None'
                    );
                    const toStatus = this.formatStatusValue(item.toString);
                    return `Moved the issue from ${fromStatus} to ${toStatus}`;

                case 'assignee':
                    if (!item.fromString) {
                        const assignedTo = this.formatAssigneeValue(
                            item.toString || ''
                        );
                        return `Assigned the issue to ${assignedTo}`;
                    }
                    const fromAssignee = this.formatAssigneeValue(
                        item.fromString || 'unassigned'
                    );
                    const toAssignee = this.formatAssigneeValue(
                        item.toString || ''
                    );
                    return `Reassigned the issue from ${fromAssignee} to ${toAssignee}`;

                case 'resolution':
                    return `Resolved the issue as <strong>${item.toString}</strong>`;

                case 'labels':
                    return `Added the "<strong>${item.toString}</strong>" label`;

                default:
                    const fieldName = fieldId || 'unknown';
                    let message = `Updated the "<strong>${fieldName}</strong>" field`;
                    if (item.fromString) {
                        message += ` from "<strong>${item.fromString}</strong>"`;
                    }
                    if (item.toString) {
                        message += ` to "<strong>${item.toString}</strong>"`;
                    }
                    return message;
            }
        });
    }

    /**
     * Processes a timeline event to add generated fields
     */
    processTimelineEvent(event: TimelineEvent): TimelineEvent {
        return {
            ...event,
            event_type_text: this.generateEventTypeText(event),
            changes_messages: this.generateChangeMessages(event),
        };
    }

    /**
     * Processes an array of timeline events
     */
    processTimelineEvents(events: TimelineEvent[]): TimelineEvent[] {
        return events.map((event) => this.processTimelineEvent(event));
    }

    // Private helper methods

    /**
     * Determines if a repo:push event represents a branch closure
     */
    private isBranchClosure(event: TimelineEvent): boolean {
        // Use the database-provided is_pr_branch_closed field
        return event.is_pr_branch_closed === true;
    }

    /**
     * Formats a message with consistent styling
     */
    private formatMessage(message: string): string {
        return message;
    }

    /**
     * Gets a color class for an event type
     */
    getEventColor(eventType: string): string {
        switch (eventType) {
            case 'pullrequest:created':
                return 'text-blue-600';
            case 'pullrequest:fulfilled':
                return 'text-green-600';
            case 'pullrequest:approved':
                return 'text-emerald-600';
            case 'pullrequest:changes_request_created':
                return 'text-orange-600';
            case 'repo:push':
                return 'text-purple-600';
            case 'jira:issue_created':
                return 'text-blue-600';
            case 'jira:issue_updated':
                return 'text-gray-600';
            case 'comment_created':
                return 'text-indigo-600';
            default:
                return 'text-gray-600';
        }
    }

    /**
     * Gets the CSS styles for status badges
     */
    private getStatusBadgeStyle(status: string): string {
        switch (status.toLowerCase()) {
            case 'for qa':
                return 'background-color: #1E88E5';
            case 'to do':
                return 'background-color: #FB8C00';
            case 'in progress':
                return 'background-color: #E7D48D; color: #000';
            case 'code review':
                return 'background-color: #6A1B9A';
            case 'for testing':
                return 'background-color: #8E24AA';
            case 'in testing':
                return 'background-color: #AB47BC';
            case 'done':
                return 'background-color: #2E7D32';
            default:
                return 'background-color: #9E9E9E';
        }
    }

    /**
     * Formats an actor name with consistent styling
     */
    // private formatActor(actor: string): string {
    //     return `<span class="font-medium text-gray-900">${actor}</span>`;
    // }

    /**
     * Formats a status value with badge styling
     */
    private formatStatusValue(value: string): string {
        return `<span class="inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-medium text-white" style="${this.getStatusBadgeStyle(
            value
        )}">${value}</span>`;
    }

    /**
     * Formats an assignee value with person icon
     */
    private formatAssigneeValue(value: string): string {
        return `<span class="inline-flex items-center gap-1.5 font-medium text-neutral-800">${this.personIcon}${value}</span>`;
    }
}
