import { Routes } from '@angular/router';
import { IssuesListComponent } from './components/issues-list/issues-list';
import { IssueDetailsComponent } from './components/issue-details/issue-details';

export const routes: Routes = [
  { path: '', redirectTo: '/issues', pathMatch: 'full' },
  { path: 'issues', component: IssuesListComponent },
  { path: 'issues/:id', component: IssueDetailsComponent }
];
