import { Injectable, signal, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, map } from 'rxjs';
import { IssueModel, IssueDetailsResponse, GroupedStatistics } from './types';
import { environment } from '../environments/environment';
import { TimelineMessageService } from './services/timeline-message.service';

@Injectable({
    providedIn: 'root',
})
export class IssueService {
    private http = inject(HttpClient);
    private timelineMessageService = inject(TimelineMessageService);
    private readonly apiUrl = environment.API_URL;

    private issuesSignal = signal<IssueModel[]>([]);
    issues = this.issuesSignal.asReadonly();

    loadIssues(
        startDate?: Date | null,
        endDate?: Date | null
    ): Observable<IssueModel[]> {
        let params = new HttpParams();
        if (startDate) {
            params = params.set('startDate', startDate.toISOString());
        }
        if (endDate) {
            params = params.set('endDate', endDate.toISOString());
        }
        return this.http.get<IssueModel[]>(this.apiUrl, { params });
    }

    getIssueDetails(issueKey: string): Observable<IssueDetailsResponse> {
        return this.http
            .get<IssueDetailsResponse>(`${this.apiUrl}/${issueKey}`)
            .pipe(
                map((response) => ({
                    ...response,
                    timeline: this.timelineMessageService.processTimelineEvents(
                        response.timeline
                    ),
                }))
            );
    }

    getStatistics(
        startDate?: Date | null,
        endDate?: Date | null
    ): Observable<GroupedStatistics> {
        let params = new HttpParams();
        if (startDate) {
            params = params.set('startDate', startDate.toISOString());
        }
        if (endDate) {
            params = params.set('endDate', endDate.toISOString());
        }
        return this.http.get<GroupedStatistics>(`${this.apiUrl}/statistics`, {
            params,
        });
    }

    getIssueByKey(key: string): IssueModel | undefined {
        return this.issuesSignal().find((issue) => issue.issue === key);
    }

    updateIssuesSignal(issues: IssueModel[]): void {
        this.issuesSignal.set(issues);
    }

    searchIssues(query: string, dateRange?: [Date, Date] | null): IssueModel[] {
        let issues = this.issuesSignal();

        // Apply text search filter
        if (query.trim()) {
            const lowerQuery = query.toLowerCase();
            issues = issues.filter(
                (issue) =>
                    issue.issue.toLowerCase().includes(lowerQuery) ||
                    issue.issue_summary.toLowerCase().includes(lowerQuery) ||
                    issue.assignee_name.toLowerCase().includes(lowerQuery) ||
                    issue.status.toLowerCase().includes(lowerQuery) ||
                    issue.issue_type.toLowerCase().includes(lowerQuery)
            );
        }

        // Apply date range filter if dates are selected
        if (dateRange && dateRange[0] && dateRange[1]) {
            const startDate = new Date(dateRange[0]);
            const endDate = new Date(dateRange[1]);
            // Set end date to end of day for inclusive filtering
            endDate.setHours(23, 59, 59, 999);

            issues = issues.filter((issue: IssueModel) => {
                const issueDate = new Date(issue.updated_at);
                return issueDate >= startDate && issueDate <= endDate;
            });
        }

        return issues;
    }
}
