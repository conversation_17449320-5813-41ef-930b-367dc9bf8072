import {
    Component,
    OnInit,
    inject,
    signal,
    ChangeDetectionStrategy,
    ViewChild,
    ElementRef,
    AfterViewInit,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IssueService } from '../../issue';
import { IssueDetailsResponse } from '../../types';
import { IssueHeaderComponent } from './components/issue-header/issue-header.component';
import { IssueSummaryComponent } from './components/issue-summary/issue-summary.component';
import { IssueProgressComponent } from './components/issue-progress/issue-progress.component';
import { IssueTimelineComponent } from './components/issue-timeline/issue-timeline.component';
import { MinimapComponent } from './components/minimap/minimap.component';

@Component({
    selector: 'app-issue-details',
    imports: [
        CommonModule,
        IssueHeaderComponent,
        IssueSummaryComponent,
        IssueProgressComponent,
        IssueTimelineComponent,
        MinimapComponent,
    ],
    templateUrl: './issue-details.html',
    styleUrl: './issue-details.css',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IssueDetailsComponent implements OnInit, AfterViewInit {
    @ViewChild('timelineContainer') timelineContainer!: ElementRef<HTMLElement>;
    private issueService = inject(IssueService);
    private route = inject(ActivatedRoute);
    private router = inject(Router);
    private statuses = [
        'For QA',
        'To Do',
        'In Progress',
        'Code review',
        'For Testing',
        'Done',
    ];
    issueDetails = signal<IssueDetailsResponse | undefined>(undefined);

    ngAfterViewInit() {
        // Initialization after view is ready
    }

    ngOnInit() {
        this.route.params.subscribe((params) => {
            const issueKey = params['id'];
            this.issueService.getIssueDetails(issueKey).subscribe((details) => {
                details.status_progress = this.statuses.map((status) => {
                    return {
                        status,
                        time_in_status:
                            details.status_progress.find(
                                (s) => s.status === status
                            )?.time_in_status || '',
                    };
                });

                this.issueDetails.set(details);
            });
        });
    }

    navigateBack() {
        this.router.navigate(['/issues']);
    }
}
