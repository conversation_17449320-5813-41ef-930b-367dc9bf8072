import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    effect,
    ElementRef,
    inject,
    input,
    signal,
    ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NzTooltipModule } from 'ng-zorro-antd/tooltip';
import { BehaviorSubject, fromEvent } from 'rxjs';
import {
    distinctUntilChanged,
    filter,
    map,
    switchMap,
    takeUntil,
    tap,
    throttleTime,
} from 'rxjs/operators';
import { TimelineEvent } from '../../../../types/timeline-event.interface';

/**
 * Represents a marker on the minimap corresponding to a timeline event
 */
export interface MinimapMarker {
    /** Type of event marker for styling purposes */
    type: MarkerType;
    /** Vertical position as percentage (0-100) */
    position: number;
    /** Index of the corresponding timeline event */
    eventIndex: number;
    /** Display name for the event type */
    eventName: string;
    /** Formatted date string for display */
    formattedDate: string;
}

/**
 * Represents a time scale marker on the minimap
 */
export interface TimeMarker {
    /** Display label for the time marker */
    label: string;
    /** Vertical position as percentage (0-100) */
    position: number;
}

/**
 * Available marker types for timeline events
 */
export type MarkerType = 'comment' | 'status' | 'pr' | 'commit';

/**
 * Configuration constants for the minimap component
 */
const MINIMAP_CONFIG = {
    /** Maximum position percentage for markers and viewport */
    MAX_POSITION: 100,
    /** Viewport height in pixels */
    VIEWPORT_HEIGHT: 80,
    /** Time marker positions for predefined intervals */
    TIME_MARKERS: {
        TODAY: 0,
        THREE_DAYS: 40,
        FIVE_DAYS: 70,
        OLDEST: 95,
    },
    /** Minimum number of events required to show intermediate time markers */
    MIN_EVENTS_FOR_MARKERS: 3,
} as const;

/**
 * Minimap component that provides a visual overview and navigation for timeline events.
 * Features include:
 * - Visual markers for different event types
 * - Draggable viewport for scroll navigation
 * - Click-to-scroll functionality
 * - Time scale markers for temporal reference
 */
@Component({
    selector: 'app-minimap',
    imports: [CommonModule, NzTooltipModule],
    templateUrl: './minimap.component.html',
    styleUrl: './minimap.component.css',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MinimapComponent implements AfterViewInit {
    // Template references
    @ViewChild('viewport') private readonly viewport!: ElementRef<HTMLElement>;
    @ViewChild('minimapTrack')
    private readonly minimapTrack!: ElementRef<HTMLElement>;

    // Component inputs
    readonly timeline = input.required<TimelineEvent[]>();
    readonly scrollableContainer = input.required<HTMLElement>();

    // Reactive state signals
    readonly viewportPosition = signal(0);
    readonly isDragging = signal(false);
    readonly hoveredMarker = signal<MinimapMarker | null>(null);

    // Reactive streams subjects
    private readonly scrollContainer$ = new BehaviorSubject<HTMLElement | null>(
        null
    );
    private readonly viewportElement$ = new BehaviorSubject<HTMLElement | null>(
        null
    );
    private readonly trackElement$ = new BehaviorSubject<HTMLElement | null>(
        null
    );
    // private readonly destroy$ = new Subject<void>();

    // Drag state
    private dragStartY = 0;
    private dragStartTop = 0;

    /**
     * Computed property that generates markers for timeline events
     */
    readonly eventMarkers = computed(() => {
        const events = this.timeline();
        if (!events.length) return [];

        return events.map((event, index) => ({
            type: this.determineMarkerType(event.event_type),
            position: this.calculateMarkerPosition(index, events.length),
            eventIndex: index,
            eventName: this.getEventDisplayName(event.event_type),
            formattedDate: this.formatEventDate(event.created_at),
        }));
    });

    /**
     * Computed property that generates time scale markers
     */
    readonly timeMarkers = computed(() => {
        const events = this.timeline();
        if (!events.length) return [];

        return this.generateTimeMarkers(events);
    });

    /**
     * Computed property for enhanced marker interactions
     */
    readonly enhancedMarkers = computed(() => {
        const markers = this.eventMarkers();
        const hoveredMarker = this.hoveredMarker();

        return markers.map((marker) => ({
            ...marker,
            isHovered: hoveredMarker?.eventIndex === marker.eventIndex,
            isNearHovered: hoveredMarker
                ? Math.abs(hoveredMarker.eventIndex - marker.eventIndex) <= 1
                : false,
        }));
    });

    private destroyRef = inject(DestroyRef);
    constructor() {
        effect(() => {
            const container = this.scrollableContainer();
            this.scrollContainer$.next(container);
        });
    }
    // Lifecycle hooks
    ngAfterViewInit(): void {
        this.initializeReactiveStreams();
    }

    // Reactive stream initialization
    private initializeReactiveStreams(): void {
        // Initialize element subjects
        this.scrollContainer$.next(this.scrollableContainer());
        this.viewportElement$.next(this.viewport.nativeElement);
        this.trackElement$.next(this.minimapTrack.nativeElement);

        this.setupScrollStream();
        this.setupDragStreams();
        this.setupClickStream();
        this.setupContainerChangeEffect();
    }

    private setupContainerChangeEffect(): void {}

    private setupScrollStream(): void {
        this.scrollContainer$
            .pipe(
                filter((container): container is HTMLElement => !!container),
                switchMap((container) =>
                    fromEvent(container, 'scroll').pipe(
                        throttleTime(16), // ~60fps
                        map(() => container),
                        distinctUntilChanged(
                            (prev, curr) => prev.scrollTop === curr.scrollTop
                        )
                    )
                ),
                filter(() => !this.isDragging()),
                map((container) => this.calculateScrollPercentage(container)),
                map((scrollPercentage) =>
                    Math.min(
                        scrollPercentage * (MINIMAP_CONFIG.MAX_POSITION / 100),
                        MINIMAP_CONFIG.MAX_POSITION
                    )
                ),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe((viewportPosition) => {
                this.viewportPosition.set(viewportPosition);
            });
    }

    private setupDragStreams(): void {
        const mouseDown$ = this.viewportElement$.pipe(
            filter((element): element is HTMLElement => !!element),
            switchMap((element) => fromEvent<MouseEvent>(element, 'mousedown'))
        );

        const mouseMove$ = fromEvent<MouseEvent>(document, 'mousemove');
        const mouseUp$ = fromEvent<MouseEvent>(document, 'mouseup');

        // Mouse down stream - start dragging
        mouseDown$
            .pipe(
                tap((e) => {
                    this.isDragging.set(true);
                    this.dragStartY = e.clientY;
                    this.dragStartTop = this.viewport.nativeElement.offsetTop;
                    this.viewport.nativeElement.style.cursor = 'grabbing';
                    e.stopPropagation();
                }),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe();

        // Mouse move stream - handle dragging
        mouseDown$
            .pipe(
                switchMap(() =>
                    mouseMove$.pipe(
                        throttleTime(16), // ~60fps
                        takeUntil(mouseUp$),
                        map((e: MouseEvent) => ({
                            deltaY: e.clientY - this.dragStartY,
                            newTop:
                                this.dragStartTop +
                                (e.clientY - this.dragStartY),
                        }))
                    )
                ),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(({ newTop }) => {
                const { constrainedTop, scrollPercentage } =
                    this.calculateDragPosition(newTop);
                this.updateViewportPosition(constrainedTop);
                this.syncScrollPosition(scrollPercentage);
            });

        // Mouse up stream - end dragging
        mouseUp$
            .pipe(
                filter(() => this.isDragging()),
                tap(() => {
                    this.isDragging.set(false);
                    this.viewport.nativeElement.style.cursor = 'grab';
                }),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe();
    }

    private setupClickStream(): void {
        this.trackElement$
            .pipe(
                filter((element): element is HTMLElement => !!element),
                switchMap((element) =>
                    fromEvent<MouseEvent>(element, 'click').pipe(
                        filter((e) => e.target !== this.viewport.nativeElement),
                        map((e) => this.calculateClickPosition(e)),
                        map((clickPosition) =>
                            this.calculateDragPosition(clickPosition)
                        )
                    )
                ),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(({ constrainedTop, scrollPercentage }) => {
                this.updateViewportPosition(constrainedTop);
                this.scrollToPosition(scrollPercentage);
            });
    }

    // Utility methods

    /**
     * Determines the marker type based on the event type string
     */
    private determineMarkerType(eventType: string): MarkerType {
        if (eventType.includes('comment') || eventType.includes('worklog')) {
            return 'comment';
        }
        if (
            eventType.includes('status') ||
            eventType.includes('issue_updated')
        ) {
            return 'status';
        }
        if (eventType.includes('pullrequest') || eventType.includes('pr')) {
            return 'pr';
        }
        return 'commit';
    }

    /**
     * Calculates the vertical position percentage for a marker
     */
    private calculateMarkerPosition(
        index: number,
        totalEvents: number
    ): number {
        if (totalEvents <= 1) return 0;
        return (index / (totalEvents - 1)) * MINIMAP_CONFIG.MAX_POSITION;
    }

    /**
     * Generates time markers for the minimap
     */
    private generateTimeMarkers(events: TimelineEvent[]): TimeMarker[] {
        const markers: TimeMarker[] = [];
        const totalEvents = events.length;

        if (totalEvents === 0) return markers;

        // Always add "Today" marker
        markers.push({
            label: 'Today',
            position: MINIMAP_CONFIG.TIME_MARKERS.TODAY,
        });

        // Add intermediate markers if there are enough events
        if (totalEvents > MINIMAP_CONFIG.MIN_EVENTS_FOR_MARKERS) {
            markers.push(
                {
                    label: '3d ago',
                    position: MINIMAP_CONFIG.TIME_MARKERS.THREE_DAYS,
                },
                {
                    label: '5d ago',
                    position: MINIMAP_CONFIG.TIME_MARKERS.FIVE_DAYS,
                }
            );
        }

        // Add oldest event marker
        const oldestEvent = events[events.length - 1];
        if (oldestEvent) {
            const formattedDate = this.formatEventDate(oldestEvent.created_at);
            markers.push({
                label: formattedDate,
                position: MINIMAP_CONFIG.TIME_MARKERS.OLDEST,
            });
        }

        return markers;
    }

    /**
     * Formats a date string for display in time markers
     */
    private formatEventDate(dateString: string): string {
        const date = new Date(dateString);
        return (
            date.toLocaleDateString('en-US', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
            }) +
            ', ' +
            date.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false,
            })
        );
    }

    /**
     * Gets a human-readable display name for an event type
     */
    private getEventDisplayName(eventType: string): string {
        if (eventType.includes('comment')) {
            return 'Comment Added';
        }
        if (eventType.includes('worklog')) {
            return 'Work Logged';
        }
        if (
            eventType.includes('status') ||
            eventType.includes('issue_updated')
        ) {
            return 'Issue Updated';
        }
        if (eventType.includes('pullrequest:created')) {
            return 'Pull Request Created';
        }
        if (eventType.includes('pullrequest:updated')) {
            return 'Pull Request Updated';
        }
        if (eventType.includes('pullrequest:merged')) {
            return 'Pull Request Merged';
        }
        if (eventType.includes('pullrequest')) {
            return 'Pull Request Event';
        }
        if (eventType.includes('commit')) {
            return 'Commit';
        }

        // Fallback: capitalize and format the event type
        return eventType
            .split(':')
            .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
            .join(' ');
    }

    // Reactive marker hover handling
    onMarkerHover(marker: MinimapMarker | null): void {
        this.hoveredMarker.set(marker);
    }

    // Public API

    /**
     * Scrolls to a specific timeline event when a marker is clicked and adds highlight effect
     */
    scrollToEvent(marker: MinimapMarker): void {
        const container = this.scrollableContainer();
        const eventElements = container.querySelectorAll('.timeline-event');
        const targetElement = eventElements[marker.eventIndex] as HTMLElement;

        if (targetElement) {
            // Scroll to the target element
            targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
            });

            // Add highlight effect after a short delay to ensure scroll is visible
            setTimeout(() => {
                this.addHighlightEffect(targetElement);
            }, 300);
        }
    }

    /**
     * Adds a temporary highlight effect to a timeline event element
     */
    private addHighlightEffect(element: HTMLElement): void {
        // Remove any existing highlight classes
        element.classList.remove('minimap-highlight', 'minimap-highlight-fade');

        // Force reflow to ensure class removal takes effect
        element.offsetHeight;

        // Add highlight class
        element.classList.add('minimap-highlight');

        // Start fade out after a brief moment
        setTimeout(() => {
            element.classList.add('minimap-highlight-fade');
        }, 100);

        // Remove all highlight classes after animation completes
        setTimeout(() => {
            element.classList.remove(
                'minimap-highlight',
                'minimap-highlight-fade'
            );
        }, 900);
    }

    // Helper methods

    /**
     * Calculates scroll percentage for the container relative to timeline content
     */
    private calculateScrollPercentage(container: HTMLElement): number {
        const timelineOffset = this.getTimelineOffset(container);
        const timelineHeight = this.getTimelineHeight(container);
        const visibleHeight = container.clientHeight;

        // Calculate how much of the timeline is scrolled past the top
        const timelineScrollTop = Math.max(
            0,
            container.scrollTop - timelineOffset
        );

        // Calculate the maximum scrollable distance within the timeline
        const maxTimelineScroll = Math.max(
            0,
            timelineHeight - visibleHeight + timelineOffset
        );

        if (maxTimelineScroll <= 0) return 0;

        // Calculate percentage based on timeline content only
        return Math.min(100, (timelineScrollTop / maxTimelineScroll) * 100);
    }

    /**
     * Gets the vertical offset of the timeline content from the top of the container
     */
    private getTimelineOffset(container: HTMLElement): number {
        const timelineElement = container.querySelector('.timeline-event');
        if (!timelineElement) return 0;

        const containerRect = container.getBoundingClientRect();
        const timelineRect = timelineElement.getBoundingClientRect();

        // Calculate the offset from container top to first timeline event
        return Math.max(
            0,
            timelineRect.top - containerRect.top + container.scrollTop
        );
    }

    /**
     * Gets the total height of the timeline content
     */
    private getTimelineHeight(container: HTMLElement): number {
        const timelineEvents = container.querySelectorAll('.timeline-event');
        if (timelineEvents.length === 0) return 0;

        const firstEvent = timelineEvents[0] as HTMLElement;
        const lastEvent = timelineEvents[
            timelineEvents.length - 1
        ] as HTMLElement;

        const firstRect = firstEvent.getBoundingClientRect();
        const lastRect = lastEvent.getBoundingClientRect();

        // Calculate total height from first to last timeline event
        return lastRect.bottom - firstRect.top;
    }

    /**
     * Calculates click position relative to the track
     */
    private calculateClickPosition(e: MouseEvent): number {
        const rect = this.minimapTrack.nativeElement.getBoundingClientRect();
        const clickY = e.clientY - rect.top;
        const viewportHeight = this.viewport.nativeElement.offsetHeight;
        return clickY - viewportHeight / 2;
    }

    /**
     * Calculates constrained position and scroll percentage for dragging
     */
    private calculateDragPosition(newTop: number): {
        constrainedTop: number;
        scrollPercentage: number;
    } {
        const trackHeight = this.minimapTrack.nativeElement.offsetHeight;
        const viewportHeight = this.viewport.nativeElement.offsetHeight;
        const maxTop = trackHeight - viewportHeight;

        const constrainedTop = Math.max(0, Math.min(newTop, maxTop));
        const scrollPercentage = maxTop > 0 ? constrainedTop / maxTop : 0;

        return { constrainedTop, scrollPercentage };
    }

    /**
     * Updates the viewport position in the DOM
     */
    private updateViewportPosition(top: number): void {
        this.viewport.nativeElement.style.top = `${top}px`;
    }

    /**
     * Synchronizes the main container scroll position accounting for timeline offset
     */
    private syncScrollPosition(scrollPercentage: number): void {
        const container = this.scrollableContainer();
        const timelineOffset = this.getTimelineOffset(container);
        const timelineHeight = this.getTimelineHeight(container);
        const visibleHeight = container.clientHeight;

        // Calculate the maximum scrollable distance within the timeline
        const maxTimelineScroll = Math.max(
            0,
            timelineHeight - visibleHeight + timelineOffset
        );

        // Calculate the target scroll position within the timeline
        const timelineScrollTop = scrollPercentage * maxTimelineScroll;

        // Add the timeline offset to get the absolute scroll position
        container.scrollTop = timelineScrollTop + timelineOffset;
    }

    /**
     * Scrolls to a position with smooth behavior accounting for timeline offset
     */
    private scrollToPosition(scrollPercentage: number): void {
        const container = this.scrollableContainer();
        const timelineOffset = this.getTimelineOffset(container);
        const timelineHeight = this.getTimelineHeight(container);
        const visibleHeight = container.clientHeight;

        // Calculate the maximum scrollable distance within the timeline
        const maxTimelineScroll = Math.max(
            0,
            timelineHeight - visibleHeight + timelineOffset
        );

        // Calculate the target scroll position within the timeline
        const timelineScrollTop = scrollPercentage * maxTimelineScroll;

        // Add the timeline offset to get the absolute scroll position
        const scrollTo = timelineScrollTop + timelineOffset;

        container.scrollTo({
            top: scrollTo,
            behavior: 'smooth',
        });
    }
}
