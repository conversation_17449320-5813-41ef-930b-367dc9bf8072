<div class="minimap" #minimap>
    <div class="minimap-track" #minimapTrack>
        <div
            class="minimap-viewport"
            #viewport
            [style.top.%]="viewportPosition()"
        ></div>

        <div
            *ngFor="let marker of timeMarkers()"
            class="time-scale-marker"
            [style.top.%]="marker.position"
        >
            {{ marker.label }}
        </div>

        <div
            *ngFor="let event of enhancedMarkers()"
            class="minimap-marker"
            [ngClass]="[
                event.type,
                event.isHovered ? 'hovered' : '',
                event.isNearHovered ? 'near-hovered' : ''
            ]"
            [style.top.%]="event.position"
            [nz-tooltip]="event.eventName + ' - ' + event.formattedDate"
            nzTooltipPlacement="left"
            [nzTooltipMouseEnterDelay]="0.3"
            [nzTooltipMouseLeaveDelay]="0.1"
            (click)="scrollToEvent(event)"
            (mouseenter)="onMarkerHover(event)"
            (mouseleave)="onMarkerHover(null)"
        ></div>
    </div>
</div>
