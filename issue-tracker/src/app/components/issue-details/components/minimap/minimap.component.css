/* Minimap */
.minimap {
    position: fixed;
    right: 20px;
    top: 120px;
    width: 60px;
    height: 600px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    padding: 8px;
    border: 1px solid rgba(229, 231, 235, 0.8);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 100;
}

.minimap-track {
    position: relative;
    width: 100%;
    height: 100%;
    background: rgba(249, 250, 251, 0.8);
    border-radius: 4px;
    border: 1px solid rgba(229, 231, 235, 0.5);
}

.minimap-viewport {
    position: absolute;
    width: 100%;
    height: 80px;
    background: rgba(59, 130, 246, 0.15);
    border: 1px solid rgba(59, 130, 246, 0.4);
    border-radius: 4px;
    cursor: grab;
    transition: all 0.2s ease;
    user-select: none;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.minimap-viewport:hover {
    background: rgba(59, 130, 246, 0.25);
    border-color: rgba(59, 130, 246, 0.6);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

.minimap-viewport:active {
    cursor: grabbing;
    background: rgba(59, 130, 246, 0.35);
    border-color: #3b82f6;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.minimap-marker {
    position: absolute;
    width: 80%;
    height: 2px;
    left: 10%;
    transition: all 0.2s ease;
    cursor: pointer;
    border-radius: 1px;
    opacity: 0.8;
}

.minimap-marker:hover,
.minimap-marker.hovered {
    width: 100%;
    left: 0;
    height: 4px;
    opacity: 1;
    border-radius: 2px;
    transform: scale(1.1);
    z-index: 10;
}

.minimap-marker.near-hovered {
    opacity: 0.9;
    transform: scale(1.05);
    transition: all 0.2s ease;
}

.minimap-marker.comment {
    background: #3b82f6;
}

.minimap-marker.status {
    background: #059669;
}

.minimap-marker.pr {
    background: #7c3aed;
}

.minimap-marker.commit {
    background: #d97706;
}

.time-scale-marker {
    position: absolute;
    right: 100%;
    font-size: 9px;
    color: #6b7280;
    white-space: nowrap;
    padding-right: 4px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}
