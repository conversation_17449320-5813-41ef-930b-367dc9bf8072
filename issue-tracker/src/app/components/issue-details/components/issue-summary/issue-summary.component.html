@if (issueDetails()) {
<!-- Breadcrumb -->
<nav aria-label="Breadcrumb" class="mb-6 text-sm font-medium text-gray-500">
    <ol class="flex items-center space-x-2">
        <li>
            <a
                class="hover:text-gray-700 cursor-pointer"
                (click)="onNavigateBack()"
                >Projects</a
            >
        </li>
        <li><span class="text-gray-400">/</span></li>
        <li>
            <a class="hover:text-gray-700" href="#">Agrimi</a>
        </li>
        <li><span class="text-gray-400">/</span></li>
        <li>
            <span class="font-semibold text-gray-800">{{
                issueDetails()!.issue_key
            }}</span>
        </li>
    </ol>
</nav>

<!-- Issue Summary -->
<div class="mb-8">
    <h1 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
        {{ issueDetails()!.issue_summary }}
    </h1>
    <div
        class="mt-2 flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-gray-500"
    >
        <span
            >Assigned to:
            <span class="font-medium text-gray-700">{{
                issueDetails()!.assignee_name
            }}</span>
        </span>
        <span class="hidden sm:inline">|</span>
        <span
            >Status:
            <span
                class="inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-medium text-white"
                [ngStyle]="issueDetails()!.current_status | statusBadgeStyle"
            >
                {{ issueDetails()!.current_status }}
            </span>
        </span>
        <span class="hidden sm:inline">|</span>
        <span
            >Priority:
            <span class="inline-flex items-center gap-2">
                <img
                    [src]="issueDetails()!.issue_priority_icon"
                    [alt]="issueDetails()!.issue_priority"
                    class="w-4 h-4"
                    (error)="$event.target.style.display = 'none'"
                />
                <span
                    [class]="
                        getPriorityBadgeClass(issueDetails()!.issue_priority)
                    "
                >
                    {{ issueDetails()!.issue_priority }}
                </span>
            </span>
        </span>
        <span
            >Jira:
            <span class="inline-flex items-center gap-2">
                <a
                    href="https://technofarm.atlassian.net/browse/{{
                        issueDetails()!.issue_key
                    }}"
                    class="text-blue-600 hover:text-blue-800 underline"
                    target="_blank"
                    rel="noopener noreferrer"
                    >{{ issueDetails()!.issue_key }}
                    <nz-icon nzType="export" nzTheme="outline" />
                </a>
            </span>
        </span>
    </div>
</div>
}
