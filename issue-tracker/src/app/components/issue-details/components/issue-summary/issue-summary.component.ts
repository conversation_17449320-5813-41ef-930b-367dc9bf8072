import { Component, input, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IssueDetails } from '../../../../types';
import { StatusBadgeStylePipe } from '../../../../pipes/status-badge-style.pipe';
import { NzIconModule } from 'ng-zorro-antd/icon';

@Component({
    selector: 'app-issue-summary',
    imports: [CommonModule, StatusBadgeStylePipe, NzIconModule],
    templateUrl: './issue-summary.component.html',
    styleUrl: './issue-summary.component.css',
})
export class IssueSummaryComponent {
    issueDetails = input<IssueDetails>();
    navigateBack = output<void>();

    onNavigateBack() {
        this.navigateBack.emit();
    }

    getPriorityBadgeClass(priority: string): string {
        const baseClass =
            'inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-medium';

        switch (priority) {
            case 'High':
                return `${baseClass} bg-red-100 text-red-800`;
            case 'Medium':
                return `${baseClass} bg-yellow-100 text-yellow-800`;
            case 'Low':
                return `${baseClass} bg-green-100 text-green-800`;
            default:
                return `${baseClass} bg-gray-100 text-gray-800`;
        }
    }
}
