<div class="mb-6 mt-6 space-y-4">
    <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold text-neutral-800">Timeline</h2>
        <div class="flex items-center gap-3">
            <!-- Actor Filter Select -->
            <div class="min-w-[180px]">
                <nz-select
                    nzMode="tags"
                    nzPlaceHolder="Filter by actor"
                    [nzMaxTagCount]="1"
                    [nzShowArrow]="true"
                    nzAllowClear
                    nzSize="small"
                    [ngModel]="selectedActors()"
                    (ngModelChange)="onActorSelectionChange($event)"
                    class="w-full"
                >
                    @for (actor of actors(); track actor) {
                    <nz-option [nzValue]="actor" [nzLabel]="actor"></nz-option>
                    }
                </nz-select>
            </div>

            <!-- Event Type Filter Select -->
            <div class="min-w-[180px]">
                <nz-select
                    nzMode="tags"
                    nzPlaceHolder="Filter by event type"
                    [nzMaxTagCount]="1"
                    [nzShowArrow]="true"
                    nzAllowClear
                    nzSize="small"
                    [ngModel]="selectedEvents()"
                    (ngModelChange)="onEventSelectionChange($event)"
                    class="w-full"
                >
                    @for (event of events(); track event) {
                    <nz-option [nzValue]="event" [nzLabel]="event"></nz-option>
                    }
                </nz-select>
            </div>

            <!-- Clear All Filters Button -->
            <button
                class="rounded-md p-1.5 text-neutral-500 hover:bg-neutral-100 hover:text-neutral-700"
                (click)="clearFilters()"
                title="Clear all filters"
            >
                <svg
                    class="h-5 w-5"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <line x1="18" x2="6" y1="6" y2="18"></line>
                    <line x1="6" x2="18" y1="6" y2="18"></line>
                </svg>
            </button>
        </div>
    </div>
</div>
<div class="flow-root">
    <ul class="-mb-8" role="list">
        @for (event of filteredTimeline(); track event.created_at; let isLast =
        $last) {
        <li>
            <div class="relative" [class.pb-2]="!isLast">
                @if (!isLast) {
                <span
                    aria-hidden="true"
                    class="absolute left-5 top-5 -ml-px h-full w-0.5 bg-gray-200"
                ></span>
                }
                <div class="relative flex items-start space-x-3">
                    <div
                        class="flex size-10 items-center justify-center rounded-md bg-white ring-8 ring-gray-50"
                    >
                        @switch (event.source) { @case ('jira') {
                        <img src="jira-icon.png" alt="Jira Icon" />
                        } @case ('bitbucket') {
                        <img src="bb-icon.png" alt="bitbucketIcon" />
                        } }
                    </div>
                    <div
                        class="timeline-event min-w-0 flex-1 rounded-lg bg-white p-4 shadow-sm ring-1 ring-gray-200/50"
                    >
                        <div>
                            <p class="text-sm text-gray-500">
                                <span
                                    [innerHTML]="
                                        event.event_type_text ||
                                        event.event_type
                                    "
                                ></span>
                            </p>
                            @if (event.changes_messages &&
                            event.changes_messages.length) {
                            <ul
                                class="mt-2 list-disc space-y-1 pl-5 text-sm text-neutral-600"
                            >
                                @for (message of event.changes_messages; track
                                message) {
                                <li
                                    [innerHTML]="message"
                                ></li>
                                }
                            </ul>
                            } @if(event.event_type === 'pullrequest:created') {
                            <div
                                class="mt-2 text-sm bg-neutral-100 ng-star-inserted p-1.5 rounded-md"
                            >
                                @if(event.pr_link) {
                                <div class="flex items-center gap-2">
                                    <img
                                        src="git-pull-request-svgrepo-com.svg"
                                        alt="Pull Request Icon"
                                        class="w-4 h-4"
                                    />
                                    <a
                                        [href]="event.pr_link"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        class="text-blue-600 hover:text-blue-800 underline truncate"
                                    >
                                        PR: {{ event.pr_title }}
                                    </a>
                                </div>
                                }
                            </div>
                            }
                        </div>
                        <p class="mt-2 text-sm text-gray-400">
                            {{ event.created_at | date }}
                        </p>
                    </div>
                </div>
            </div>
            @if (event.elapsed_time) {
            <div class="relative py-4 pl-14">
                <div class="flex items-center gap-2 text-xs text-neutral-400">
                    <svg
                        class="h-4 w-4"
                        fill="none"
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12,6 12,12 16,14"></polyline>
                    </svg>
                    <span>{{ event.elapsed_time }} since previous action</span>
                </div>
            </div>
            }
        </li>
        }
    </ul>
</div>
