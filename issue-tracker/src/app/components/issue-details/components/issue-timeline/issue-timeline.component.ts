import { CommonModule } from '@angular/common';
import { Component, computed, input, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { TimelineEvent } from '../../../../types';

@Component({
    selector: 'app-issue-timeline',
    imports: [CommonModule, FormsModule, NzSelectModule, NzIconModule],
    templateUrl: './issue-timeline.component.html',
    styleUrl: './issue-timeline.component.css',
})
export class IssueTimelineComponent {
    timeline = input<TimelineEvent[]>();
    actors = input<string[]>();
    events = input<string[]>();

    // Filter state
    selectedActors = signal<string[]>([]);
    selectedEvents = signal<string[]>([]);

    // Computed filtered timeline
    filteredTimeline = computed(() => {
        const timelineData = this.timeline() || [];
        const actorFilters = this.selectedActors();
        const eventFilters = this.selectedEvents();

        // If no filters are selected, return all timeline events
        if (actorFilters.length === 0 && eventFilters.length === 0) {
            return timelineData;
        }

        return timelineData.filter((event) => {
            const matchesActor =
                actorFilters.length === 0 ||
                actorFilters.includes(event.actor_name);
            const matchesEvent =
                eventFilters.length === 0 ||
                eventFilters.includes(event.event_type);

            return matchesActor && matchesEvent;
        });
    });

    onActorSelectionChange(selectedActors: string[]): void {
        this.selectedActors.set(selectedActors);
    }

    onEventSelectionChange(selectedEvents: string[]): void {
        this.selectedEvents.set(selectedEvents);
    }

    clearFilters(): void {
        this.selectedActors.set([]);
        this.selectedEvents.set([]);
    }
}
