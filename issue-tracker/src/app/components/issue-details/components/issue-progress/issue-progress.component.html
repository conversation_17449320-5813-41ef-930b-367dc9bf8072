<div class="pt-6 pb-6 bg-gray-50">
    <div
        class="flex w-full items-center gap-x-1 rounded-full bg-neutral-200 p-1"
    >
        @for (status of statusProgress(); track status.status; let isFirst =
        $first; let isLast = $last; let i = $index) {
        <div
            class="relative flex-1 py-1 text-center text-xs font-medium text-white"
            [class.bg-blue-500]="isFirst"
            [class.rounded-l-full]="isFirst"
            [class.rounded-r-full]="isLast"
            [style]="
                (status.time_in_status ? status.status : '') | statusBadgeStyle
            "
        >
            <div
                class="absolute -top-6 left-1/2 -translate-x-1/2 whitespace-nowrap text-xs text-neutral-500"
            >
                {{ status.status }}
                @if ((!isFirst || status.status !== "To Do") &&
                status.time_in_status) { ({{ status.time_in_status }}) }
            </div>
            @if (status.time_in_status && statusProgress() &&
            statusProgress()![i + 1] && statusProgress()![i + 1].time_in_status)
            { ✓ } @else { &nbsp; }
        </div>
        }
    </div>
</div>
