import { Component, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StatusProgress } from '../../../../types';
import { StatusBadgeStylePipe } from '../../../../pipes/status-badge-style.pipe';

@Component({
    selector: 'app-issue-progress',
    imports: [CommonModule, StatusBadgeStylePipe],
    templateUrl: './issue-progress.component.html',
    styleUrl: './issue-progress.component.css',
})
export class IssueProgressComponent {
    statusProgress = input<StatusProgress[]>();
}
