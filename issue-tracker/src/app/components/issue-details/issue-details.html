<div class="flex min-h-screen flex-col">
    <app-issue-header (navigateBack)="navigateBack()"></app-issue-header>
    <main class="flex-grow">
        <div class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
            <div
                class="mx-auto max-w-3xl h-[calc(100vh-130px)] overflow-y-auto"
                #timelineContainer
                id="timeline-container"
            >
                @if (issueDetails(); as details) {
                <app-issue-summary
                    [issueDetails]="details.issue_details"
                    (navigateBack)="navigateBack()"
                >
                </app-issue-summary>

                <app-issue-progress
                    [statusProgress]="details.status_progress"
                    class="sticky top-0 z-10"
                >
                </app-issue-progress>

                <app-issue-timeline
                    [timeline]="details.timeline"
                    [actors]="details.filters.actors"
                    [events]="details.filters.events"
                >
                </app-issue-timeline>

                @if (timelineContainer) { @let container = timelineContainer;
                <app-minimap
                    [timeline]="details.timeline"
                    [scrollableContainer]="container"
                >
                </app-minimap>
                } }
            </div>
        </div>
    </main>
</div>
