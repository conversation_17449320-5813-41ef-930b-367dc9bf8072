/**
 * Represents a change message within a timeline event.
 * Contains information about field changes made to an issue during updates.
 */
export interface ChangeMessage {
    /** The field that was changed (e.g., "status", "assignee", "resolution") */
    field: string;
    /** The previous value of the field, or null if not applicable */
    from: string | null;
    /** The new value of the field, or null if not applicable */
    to: string | null;
    /** Name of the person who made the change */
    actor: string;
    /** Template message describing the change with placeholders for formatting */
    msg_template: string;
}
