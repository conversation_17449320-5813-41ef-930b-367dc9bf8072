/**
 * Represents the core details of a specific issue.
 * Contains essential information about an issue without metadata like actors or events.
 */
export interface IssueDetails {
    /** Unique issue identifier (e.g., "GPS-5145") */
    issue_key: string;
    /** Type of the issue (e.g., "Task", "Bug", "Story") */
    issue_type: string;
    /** Brief description/title of the issue */
    issue_summary: string;
    /** Name of the person assigned to the issue */
    assignee_name: string;
    /** Current status of the issue (e.g., "In Progress", "Code review", "Done") */
    current_status: string;
    /** Priority level of the issue (e.g., "Спешен", "Среден", "Нисък") */
    issue_priority: string;
    /** Icon identifier for the issue priority */
    issue_priority_icon: string;
}
