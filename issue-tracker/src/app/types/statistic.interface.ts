/**
 * Represents a single statistic metric for development or QA performance.
 * Used for displaying key performance indicators and metrics in the dashboard.
 */
export interface Statistic {
    /** Total number of issues included in the statistic calculation */
    total_issues: number;
    /** Human-readable formatted value of the statistic (e.g., "2 hours 30 minutes") */
    value: string;
    /** Target time for this metric in human-readable format */
    target_time: number;
    /** Type identifier for the statistic (e.g., 'avg_time_for_code_review') */
    stat_type: string;
}
