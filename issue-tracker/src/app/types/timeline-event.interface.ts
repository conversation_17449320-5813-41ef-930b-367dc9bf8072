/**
 * Represents a timeline event in an issue's history.
 * Contains detailed information about events from both Jira and Bitbucket systems.
 */
export interface TimelineEvent {
    /** Type of event (e.g., "jira:issue_updated", "pullrequest:created") */
    event_type: string;
    /** Human-readable time elapsed since the previous event, or null for the first event */
    elapsed_time: string | null;
    /** Source system of the event ("jira" or "bitbucket") */
    source: string;
    /** Name of the person who triggered the event */
    actor_name: string;
    /** Repository name for Bitbucket events, may be empty for Jira events */
    repository: string;
    /** Pull request title for Bitbucket pull request events, null for other events */
    pr_title: string | null;
    /** Pull request link for Bitbucket pull request events, null for other events */
    pr_link: string | null;
    /** Pull request ID for Bitbucket pull request events, null for other events */
    pr_id: string | null;
    /** Pull request draft status for Bitbucket pull request events, null for other events */
    pr_is_draft: boolean | null;
    /** Indicates if the PR branch was closed (true=closed, false=active, null=non-PR events) */
    is_pr_branch_closed: boolean | null;
    /** Raw issue changelog data from the database for frontend processing */
    issue_changelog: any;
    /** Timestamp when the event occurred (ISO string format) */
    created_at: string;
    hovered: boolean;
    branch_name: string | null;

    // Frontend-generated fields
    /** Human-readable description of the event type (generated on frontend) */
    event_type_text?: string;
    /** Array of formatted HTML messages describing what was modified in this event (generated on frontend) */
    changes_messages?: string[];
}
