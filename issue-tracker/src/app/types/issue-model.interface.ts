/**
 * Represents an issue item in the issues list view.
 * Contains essential information about an issue for display in grids and lists.
 */
export interface IssueModel {
    /** Unique issue identifier (e.g., "GPS-5145") */
    issue: string;
    /** Type of the issue (e.g., "Task", "Bug", "Story") */
    issue_type: string;
    /** Priority level of the issue (e.g., "Спеше<PERSON>", "Среден", "Нисък") */
    issue_priority: string;
    /** Icon identifier for the issue priority */
    issue_priority_icon: string;
    /** Current status of the issue (e.g., "In Progress", "Code review", "Done") */
    status: string;
    /** Brief description/title of the issue */
    issue_summary: string;
    /** Name of the person assigned to the issue */
    assignee_name: string;
    /** Number of pull requests associated with this issue */
    prs: number;
    /** Timestamp of the last update to this issue (ISO string format) */
    updated_at: string;
}
