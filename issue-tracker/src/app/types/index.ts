/**
 * Type definitions for the issue tracker application.
 * These interfaces represent the structure of data used throughout the frontend.
 */

export type { IssueModel } from './issue-model.interface';
export type { ChangeMessage } from './change-message.interface';
export type { TimelineEvent } from './timeline-event.interface';
export type { StatusProgress } from './status-progress.interface';
export type { Statistic } from './statistic.interface';
export type { GroupedStatistics } from './grouped-statistics.interface';
export type { IssueDetails } from './issue-details.interface';
export type { Filters } from './filters.interface';
export type { IssueDetailsResponse } from './issue-details-response.interface';
