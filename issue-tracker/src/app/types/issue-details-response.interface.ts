import { IssueDetails } from './issue-details.interface';
import { TimelineEvent } from './timeline-event.interface';
import { StatusProgress } from './status-progress.interface';
import { Filters } from './filters.interface';

/**
 * Represents the complete response structure for issue details API calls.
 * Contains all information needed to display a comprehensive issue details view.
 */
export interface IssueDetailsResponse {
    /** Core issue details and metadata */
    issue_details: IssueDetails;
    /** Chronological timeline of events related to the issue */
    timeline: TimelineEvent[];
    /** Time spent in each status throughout the issue lifecycle */
    status_progress: StatusProgress[];
    /** Available filter options for actors and events */
    filters: Filters;
}
