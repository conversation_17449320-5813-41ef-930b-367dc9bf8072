# Base image
FROM node:22-alpine AS builder

# Create app directory
WORKDIR /var/www/html/app

# A wildcard is used to ensure both package.json AND package-lock.json are copied
COPY package*.json ./

# Install app dependencies
RUN npm install

# Bundle app source
COPY . .

# Creates a "dist" folder with the production build
RUN npm run build

# FROM node:22-alpine

# WORKDIR /var/www/html/app

# COPY --from=builder /var/www/html/app/dist /var/www/html/app/dist

# Expose the port on which the app will run
EXPOSE 3000

# Start the server using the production build
CMD ["node", "dist/main.js"]