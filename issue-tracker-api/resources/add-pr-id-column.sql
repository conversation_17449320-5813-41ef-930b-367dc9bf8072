-- Add pr_id column to events table
-- This column extracts the pull request ID from the Bitbucket data JSON

ALTER TABLE public.events 
ADD COLUMN pr_id text GENERATED ALWAYS AS (
    CASE
        WHEN source = 'bitbucket'::data_source AND event_type::text ~~ 'pullrequest:%'::text 
        THEN TRIM(BOTH '"'::text FROM jsonb_path_query_first(data, '$."pullrequest"."id"'::jsonpath)::text)
        ELSE NULL::text
    END
) STORED;

-- Add index for pr_id column for better query performance
CREATE INDEX idx_events_pr_id ON public.events USING btree (pr_id) 
WHERE (source = 'bitbucket'::data_source AND pr_id IS NOT NULL);

-- Add comment to document the column
COMMENT ON COLUMN public.events.pr_id IS 'Pull request ID extracted from Bitbucket pullrequest data, null for non-PR events';
