-- Add is_pr_branch_closed column to events table
-- This column determines if a pull request branch was closed based on the Bitbucket data

ALTER TABLE public.events 
ADD COLUMN is_pr_branch_closed boolean GENERATED ALWAYS AS (
    CASE
        WHEN source = 'bitbucket'::data_source AND event_type::text ~~ 'pullrequest:%'::text 
        THEN (
            CASE 
                WHEN TRIM(BOTH '"'::text FROM jsonb_path_query_first(data, '$."pullrequest"."state"'::jsonpath)::text) = 'MERGED'
                     OR TRIM(BOTH '"'::text FROM jsonb_path_query_first(data, '$."pullrequest"."state"'::jsonpath)::text) = 'DECLINED'
                THEN true
                ELSE false
            END
        )
        ELSE NULL
    END
) STORED;

-- Add index for is_pr_branch_closed column for better query performance
CREATE INDEX idx_events_is_pr_branch_closed ON public.events USING btree (is_pr_branch_closed) 
WHERE (source = 'bitbucket'::data_source AND is_pr_branch_closed IS NOT NULL);

-- Add comment to document the column
COMMENT ON COLUMN public.events.is_pr_branch_closed IS 'Indicates if PR branch was closed: true for MERGED/DECLINED PRs, false for OPEN PRs, null for non-PR events';
