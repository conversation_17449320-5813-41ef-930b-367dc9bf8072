-- Trigger to refresh issues_transitions materialized view on events table insert
-- This trigger automatically refreshes the materialized view whenever new events are inserted

-- First, create the trigger function
CREATE OR REPLACE FUNCTION refresh_issues_transitions_on_insert()
RETURNS TRIGGER AS $$
BEGIN
    -- Refresh the materialized view
    -- Using REFRESH MATERIALIZED VIEW CONCURRENTLY if the view has unique indexes
    -- Otherwise, use regular REFRESH MATERIALIZED VIEW
    REFRESH MATERIALIZED VIEW public.issues_transitions;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger on the events table
CREATE TRIGGER trg_refresh_issues_transitions_on_insert
    AFTER INSERT ON public.events
    FOR EACH ROW
    EXECUTE FUNCTION refresh_issues_transitions_on_insert();

-- Add comment to document the trigger
COMMENT ON TRIGGER trg_refresh_issues_transitions_on_insert ON public.events IS 
'Automatically refreshes the issues_transitions materialized view when new events are inserted';

COMMENT ON FUNCTION refresh_issues_transitions_on_insert() IS 
'Trigger function that refreshes the issues_transitions materialized view';
