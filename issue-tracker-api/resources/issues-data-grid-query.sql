-- Statement # 1
-- Query for Jira Issues Data Grid
WITH latest_jira_data AS (
    -- Get the most recent Jira event for each issue to extract summary and assignee
    SELECT DISTINCT ON (issue)
        issue,
        issue_type,
        issue_priority,
        issue_priority_icon,
        status,
        issue_summary,
        assignee_name,
        created_at
    FROM events 
    WHERE source = 'jira' 
      AND issue IS NOT NULL
      AND data IS NOT NULL
    ORDER BY issue, created_at DESC
),
pr_counts AS (
    -- Count unique pull requests per issue from Bitbucket events
    SELECT 
        issue,
        COUNT(*) as pr_count
    FROM events 
    WHERE source = 'bitbucket' 
      and event_type ='pullrequest:created'
    GROUP BY issue
),
latest_updates AS (
    -- Get the most recent update timestamp from either Jira or Bitbucket
    SELECT 
        issue,
        MAX(created_at) as last_updated
    FROM events 
    WHERE issue IS NOT NULL
    GROUP BY issue
)
SELECT 
    ljd.issue,
    ljd.issue_type,
    ljd.issue_priority,
    ljd.issue_priority_icon,
    ljd.status,
    ljd.issue_summary,
    ljd.assignee_name,
    COALESCE(pc.pr_count, 0) as prs,
    lu.last_updated as updated_at
FROM latest_jira_data ljd
LEFT JOIN pr_counts pc ON ljd.issue = pc.issue
LEFT JOIN latest_updates lu ON ljd.issue = lu.issue
ORDER BY lu.last_updated DESC;