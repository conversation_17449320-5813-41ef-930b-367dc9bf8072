-- public.events definition

-- Drop table

-- DROP TABLE public.events;

CREATE TABLE public.events ( id serial4 NOT NULL, issue varchar NULL, event_type varchar NOT NULL, "source" public.data_source NOT NULL, created_at timestamptz DEFAULT now() NOT NULL, "data" jsonb NULL, repository varchar NULL, status varchar NULL, assignee_name text GENERATED ALWAYS AS (
CASE
    WHEN source = 'jira'::data_source THEN COALESCE(TRIM(BOTH '"'::text FROM jsonb_path_query_first(data, '$."issue"."fields"."assignee"."displayName"'::jsonpath)::text), 'Unassigned'::text)
    ELSE NULL::text
END) STORED NULL, issue_summary text GENERATED ALWAYS AS (
CASE
    WHEN source = 'jira'::data_source THEN TRIM(BOTH '"'::text FROM jsonb_path_query_first(data, '$."issue"."fields"."summary"'::jsonpath)::text)
    ELSE NULL::text
END) STORED NULL, issue_type text GENERATED ALWAYS AS (
CASE
    WHEN source = 'jira'::data_source THEN TRIM(BOTH '"'::text FROM jsonb_path_query_first(data, '$."issue"."fields"."issuetype"."name"'::jsonpath)::text)
    ELSE NULL::text
END) STORED NULL, pr_title text GENERATED ALWAYS AS (
CASE
    WHEN source = 'bitbucket'::data_source AND event_type::text ~~ 'pullrequest:%'::text THEN TRIM(BOTH '"'::text FROM jsonb_path_query_first(data, '$."pullrequest"."title"'::jsonpath)::text)
    ELSE NULL::text
END) STORED NULL, pr_state text GENERATED ALWAYS AS (
CASE
    WHEN source = 'bitbucket'::data_source AND event_type::text ~~ 'pullrequest:%'::text THEN TRIM(BOTH '"'::text FROM jsonb_path_query_first(data, '$."pullrequest"."state"'::jsonpath)::text)
    ELSE NULL::text
END) STORED NULL, actor_name text GENERATED ALWAYS AS (TRIM(BOTH '"'::text FROM jsonb_path_query_first(data, '$."actor"."display_name"'::jsonpath)::text)) STORED NULL, issue_priority text GENERATED ALWAYS AS (
CASE
    WHEN source = 'jira'::data_source THEN TRIM(BOTH '"'::text FROM jsonb_path_query_first(data, '$."issue"."fields"."priority"."name"'::jsonpath)::text)
    ELSE NULL::text
END) STORED NULL, CONSTRAINT events_pkey PRIMARY KEY (id));
CREATE INDEX idx_events_assignee_name ON public.events USING btree (assignee_name) WHERE (source = 'jira'::data_source);
CREATE INDEX idx_events_data_gin ON public.events USING gin (data);
CREATE INDEX idx_events_issue_summary ON public.events USING btree (issue_summary) WHERE (source = 'jira'::data_source);
CREATE INDEX idx_events_issue_timeline ON public.events USING btree (issue, created_at);
CREATE INDEX idx_events_issue_type ON public.events USING btree (issue_type) WHERE (source = 'jira'::data_source);
CREATE INDEX idx_events_source_type ON public.events USING btree (source, event_type);

-- Table Triggers

-- Statement # 1
CREATE TRIGGER trg_set_issue_on_insert
    BEFORE INSERT ON public.events
    FOR EACH ROW
    WHEN ((new.source = 'bitbucket'::data_source))
    EXECUTE FUNCTION set_issue_from_branch_trigger ()
;