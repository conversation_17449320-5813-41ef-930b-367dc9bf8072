-- Statement # 1
WITH prs AS (
    SELECT
        issue
        , e.pr_id
        , e.event_type
        , LEAD(e.event_type) OVER (PARTITION BY e.pr_id ORDER BY e.created_at) AS next_event_type
        , LEAD(e.created_at) OVER (PARTITION BY e.pr_id ORDER BY e.created_at) AS next_change_at
        , e.repository
        , e.pr_link
        , e.created_at
    FROM
        events e
    WHERE
        source = 'bitbucket'
        AND e.event_type IN ('pullrequest:created'
            , 'pullrequest:changes_request_created'
            , 'pullrequest:rejected'
            , 'pullrequest:approved'
            , 'pullrequest:fulfilled')
    ORDER BY
        issue
        , created_at
)
, prs_trans AS (
    SELECT DISTINCT ON (pr_id)
        issue
        , pr_id
        , event_type
        , next_event_type
        , repository
        , pr_link
        , created_at
        , next_change_at
    FROM
        prs
    ORDER BY
        pr_id
        , issue
        , created_at
)
, draft_transitions AS (
    SELECT
        pr_id
        , created_at
        , pr_is_draft
        , LAG(pr_is_draft) OVER (PARTITION BY pr_id ORDER BY created_at) AS prev_pr_is_draft
    , actor_name
    , ROW_NUMBER() OVER (PARTITION BY pr_id ORDER BY created_at) AS rn
FROM
    events
    WHERE
        pr_id IS NOT NULL
        AND pr_is_draft IS NOT NULL
)
, state_changes AS (
    SELECT
        pr_id
        , created_at
        , pr_is_draft
        , prev_pr_is_draft
        , actor_name
        , rn
        , CASE WHEN pr_is_draft = TRUE
            AND (prev_pr_is_draft = FALSE
                OR prev_pr_is_draft IS NULL) THEN
            'draft_start'
        WHEN pr_is_draft = FALSE
            AND prev_pr_is_draft = TRUE THEN
            'draft_end'
        ELSE
            'no_change'
        END AS change_type
    FROM
        draft_transitions
)
, draft_starts AS (
    SELECT
        pr_id
        , created_at AS start_time
        , rn
        , actor_name AS start_actor
    FROM
        state_changes
    WHERE
        change_type = 'draft_start'
)
, draft_ends AS (
    SELECT
        pr_id
        , created_at AS end_time
        , rn
        , actor_name AS end_actor
    FROM
        state_changes
    WHERE
        change_type = 'draft_end'
)
, draft_work_time AS (
    SELECT
        ds.pr_id
        , TSTZRANGE(ds.start_time
            , COALESCE(de.end_time
                , NOW())
    , '[]') AS draft_period_range
        , ds.start_time AS draft_start
        , COALESCE(de.end_time
            , NOW()) AS draft_end
    , calculate_working_minutes (ds.start_time
        , COALESCE(de.end_time
            , NOW())) AS draft_working_time
FROM
    draft_starts ds
    LEFT JOIN draft_ends de ON de.pr_id = ds.pr_id
        AND de.rn = (
            SELECT
                MIN(rn)
            FROM
                draft_ends
        WHERE
            pr_id = ds.pr_id
            AND rn > ds.rn)
    ORDER BY
        ds.pr_id
        , ds.start_time
)
, total_draft_work_time as (
SELECT
    pr_id
    , SUM(draft_working_time) draft_working_time
FROM
    draft_work_time
GROUP BY
    pr_id
)
, work_time AS (
    SELECT
        issue
        , pr_id
        , event_type
        , next_event_type
        , repository
        , pr_link
        , created_at
        , next_change_at
        , TSTZRANGE(created_at
            , COALESCE(next_change_at
                , NOW())
    , '[]'::text) AS status_date_range
        , calculate_working_minutes (created_at
            , COALESCE(next_change_at
                , NOW())) AS working_time_in_status
    FROM
        prs_trans
    WHERE
        event_type NOT IN ('pullrequest:fulfilled'
            , 'pullrequest:rejected')
        AND event_type = 'pullrequest:created'
        AND TSTZRANGE(created_at
            , COALESCE(next_change_at
                , NOW())
        , '[]'::text) && '[2025-07-31T21:00:00.000Z, 2025-08-31T20:59:59.999Z]'::tstzrange
)
, real_work_time AS (
    SELECT
        wt.issue
        , wt.pr_id
        , wt.working_time_in_status
        , dt.draft_working_time
        , wt.working_time_in_status - COALESCE(dt.draft_working_time
            , 0) total_working_time
    FROM
        work_time wt
        LEFT JOIN total_draft_work_time dt ON dt.pr_id = wt.pr_id
)
SELECT
    COUNT(pr_id) as prs
    , AVG(total_working_time) avg_working_time
FROM
    real_work_time;