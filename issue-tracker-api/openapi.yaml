openapi: 3.0.3
info:
  title: Issue Tracker API
  description: |
    A comprehensive REST API for tracking and analyzing issues from Jira and Bitbucket.
    This API provides endpoints for retrieving issue lists, detailed issue information,
    timeline events, statistics, and performance metrics for development and QA teams.
  version: 0.0.1
  contact:
    name: Issue Tracker API Support
  license:
    name: UNLICENSED
servers:
  - url: http://localhost:3000
    description: Development server
  - url: https://api.issue-tracker.example.com
    description: Production server

paths:
  /:
    get:
      summary: Health check endpoint
      description: Simple health check that returns a greeting message
      operationId: getHello
      tags:
        - Health
      responses:
        "200":
          description: Successful response
          content:
            text/plain:
              schema:
                type: string
                example: "Hello World!"

  /issues:
    get:
      summary: Get list of issues
      description: |
        Retrieve a paginated list of issues with optional date range filtering.
        Returns basic issue information including status, priority, assignee, and summary.
      operationId: getIssuesList
      tags:
        - Issues
      parameters:
        - name: startDate
          in: query
          description: Start date for filtering issues (ISO 8601 format)
          required: false
          schema:
            type: string
            format: date-time
            example: "2024-01-01T00:00:00.000Z"
        - name: endDate
          in: query
          description: End date for filtering issues (ISO 8601 format)
          required: false
          schema:
            type: string
            format: date-time
            example: "2024-12-31T23:59:59.999Z"
      responses:
        "200":
          description: List of issues retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/IssueListItem"
        "400":
          $ref: "#/components/responses/BadRequest"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /issues/statistics:
    get:
      summary: Get grouped statistics
      description: |
        Retrieve development and QA statistics including average times for various processes
        such as code review, testing, and bug resolution within a specified date range.
      operationId: getStatistics
      tags:
        - Statistics
      parameters:
        - name: startDate
          in: query
          description: Start date for statistics calculation (ISO 8601 format)
          required: true
          schema:
            type: string
            format: date-time
            example: "2024-01-01T00:00:00.000Z"
        - name: endDate
          in: query
          description: End date for statistics calculation (ISO 8601 format)
          required: true
          schema:
            type: string
            format: date-time
            example: "2024-12-31T23:59:59.999Z"
      responses:
        "200":
          description: Statistics retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GroupedStatistics"
        "400":
          $ref: "#/components/responses/BadRequest"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /issues/statistics/{slug}:
    get:
      summary: Get issues for a specific statistic
      description: |
        Retrieve detailed information about issues that contributed to a specific statistic calculation.
        Includes individual issue metrics and the overall statistic summary.
      operationId: getStatisticIssues
      tags:
        - Statistics
      parameters:
        - name: slug
          in: path
          description: The statistic type identifier
          required: true
          schema:
            $ref: "#/components/schemas/StatisticType"
        - name: startDate
          in: query
          description: Start date for filtering issues (ISO 8601 format)
          required: false
          schema:
            type: string
            format: date-time
            example: "2024-01-01T00:00:00.000Z"
        - name: endDate
          in: query
          description: End date for filtering issues (ISO 8601 format)
          required: false
          schema:
            type: string
            format: date-time
            example: "2024-12-31T23:59:59.999Z"
      responses:
        "200":
          description: Statistic issues retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatisticIssuesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /issues/{issueKey}:
    get:
      summary: Get detailed issue information
      description: |
        Retrieve comprehensive details for a specific issue including timeline events,
        status progression, and available filters for actors and events.
      operationId: getIssueDetails
      tags:
        - Issues
      parameters:
        - name: issueKey
          in: path
          description: The unique issue identifier (e.g., GPS-5145)
          required: true
          schema:
            type: string
            pattern: '^[A-Z]+-\d+$'
            example: "GPS-5145"
      responses:
        "200":
          description: Issue details retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IssueDetailsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

components:
  schemas:
    IssueListItem:
      type: object
      description: Basic issue information for list views
      required:
        - issue
        - issue_type
        - issue_priority
        - issue_priority_icon
        - status
        - issue_summary
        - assignee_name
        - prs
        - updated_at
      properties:
        issue:
          type: string
          description: Unique issue identifier
          example: "GPS-5145"
        issue_type:
          type: string
          description: Type of the issue
          example: "Bug"
        issue_priority:
          type: string
          description: Priority level of the issue
          example: "High"
        issue_priority_icon:
          type: string
          description: Icon identifier for the priority level
          example: "priority-high"
        status:
          type: string
          description: Current status of the issue
          example: "In Progress"
        issue_summary:
          type: string
          description: Brief summary of the issue
          example: "Fix authentication bug in login flow"
        assignee_name:
          type: string
          description: Name of the person assigned to the issue
          example: "John Doe"
        prs:
          type: integer
          description: Number of pull requests associated with this issue
          example: 2
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-15T10:30:00Z"

    IssueDetails:
      type: object
      description: Detailed issue information
      required:
        - issue_key
        - issue_type
        - issue_summary
        - assignee_name
        - current_status
        - issue_priority
        - issue_priority_icon
      properties:
        issue_key:
          type: string
          description: Unique issue identifier
          example: "GPS-5145"
        issue_type:
          type: string
          description: Type of the issue
          example: "Bug"
        issue_summary:
          type: string
          description: Brief summary of the issue
          example: "Fix authentication bug in login flow"
        assignee_name:
          type: string
          description: Name of the person assigned to the issue
          example: "John Doe"
        current_status:
          type: string
          description: Current status of the issue
          example: "Code Review"
        issue_priority:
          type: string
          description: Priority level of the issue
          example: "High"
        issue_priority_icon:
          type: string
          description: Icon identifier for the priority level
          example: "priority-high"

    TimelineEvent:
      type: object
      description: A single event in the issue timeline
      required:
        - event_type
        - source
        - actor_name
        - repository
        - created_at
      properties:
        event_type:
          type: string
          description: Type of event that occurred
          example: "jira:issue_updated"
        elapsed_time:
          type: string
          nullable: true
          description: Human-readable time elapsed since previous event
          example: "2 hours 30 minutes"
        source:
          type: string
          description: Source system of the event
          enum: ["jira", "bitbucket"]
          example: "jira"
        actor_name:
          type: string
          description: Name of the person who triggered the event
          example: "Jane Smith"
        repository:
          type: string
          description: Repository name for Bitbucket events
          example: "my-project"
        pr_title:
          type: string
          nullable: true
          description: Pull request title for PR events
          example: "Fix authentication bug"
        pr_link:
          type: string
          nullable: true
          description: URL to the pull request
          example: "https://bitbucket.org/team/repo/pull-requests/123"
        pr_id:
          type: string
          nullable: true
          description: Pull request identifier
          example: "123"
        pr_is_draft:
          type: boolean
          nullable: true
          description: Whether the pull request is a draft
          example: false
        is_pr_branch_closed:
          type: boolean
          nullable: true
          description: Whether the pull request branch was closed
          example: true
        issue_changelog:
          description: Changelog data for the event
          example: {}
        created_at:
          type: string
          format: date-time
          description: When the event occurred
          example: "2024-01-15T10:30:00Z"

    ChangeMessage:
      type: object
      description: Information about a field change in an issue
      required:
        - field
        - actor
        - msg_template
      properties:
        field:
          type: string
          description: The field that was changed
          example: "status"
        from:
          type: string
          nullable: true
          description: Previous value of the field
          example: "To Do"
        to:
          type: string
          nullable: true
          description: New value of the field
          example: "In Progress"
        actor:
          type: string
          description: Name of the person who made the change
          example: "John Doe"
        msg_template:
          type: string
          description: Template message describing the change
          example: "Status changed from {from} to {to}"

    StatusProgress:
      type: object
      description: Time spent in a specific status
      required:
        - status
        - time_in_status
      properties:
        status:
          type: string
          description: Status name
          example: "Code Review"
        time_in_status:
          type: string
          description: Human-readable time spent in this status
          example: "1 day 4 hours"

    Filters:
      type: object
      description: Available filter options for issue events
      required:
        - actors
        - events
      properties:
        actors:
          type: array
          items:
            type: string
          description: List of unique actors who have interacted with the issue
          example: ["John Doe", "Jane Smith", "Bob Wilson"]
        events:
          type: array
          items:
            type: string
          description: List of unique event types that have occurred
          example:
            [
              "jira:issue_updated",
              "pullrequest:created",
              "pullrequest:approved",
            ]

    IssueDetailsResponse:
      type: object
      description: Complete response for issue details
      required:
        - issue_details
        - timeline
        - status_progress
        - filters
      properties:
        issue_details:
          $ref: "#/components/schemas/IssueDetails"
        timeline:
          type: array
          items:
            $ref: "#/components/schemas/TimelineEvent"
          description: Chronological timeline of events
        status_progress:
          type: array
          items:
            $ref: "#/components/schemas/StatusProgress"
          description: Time spent in each status
        filters:
          $ref: "#/components/schemas/Filters"

    Statistic:
      type: object
      description: A single statistic measurement
      required:
        - total_issues
        - value
        - target_time
        - stat_type
      properties:
        total_issues:
          type: integer
          description: Number of issues included in the calculation
          example: 25
        value:
          type: string
          description: Human-readable statistic value
          example: "2 hours 30 minutes"
        target_time:
          type: string
          description: Target time for this metric
          example: "4 hours"
        stat_type:
          type: string
          description: Type identifier for the statistic
          example: "avg_time_for_code_review"

    GroupedStatistics:
      type: object
      description: Statistics grouped by team/category
      required:
        - dev
        - qa
      properties:
        dev:
          type: array
          items:
            $ref: "#/components/schemas/Statistic"
          description: Development team statistics
        qa:
          type: array
          items:
            $ref: "#/components/schemas/Statistic"
          description: QA team statistics

    StatisticType:
      type: string
      description: Available statistic types
      enum:
        - "avg_time_for_code_review"
        - "avg_time_for_bug_for_qa_to_todo"
        - "avg_time_for_testing"
      example: "avg_time_for_code_review"

    StatisticIssue:
      type: object
      description: Issue information with statistic-specific data
      required:
        - issue
        - issue_type
        - issue_priority
        - issue_priority_icon
        - status
        - issue_summary
        - assignee_name
        - prs
        - updated_at
        - statistic_value
        - formatted_statistic_value
      properties:
        issue:
          type: string
          description: Unique issue identifier
          example: "GPS-5145"
        issue_type:
          type: string
          description: Type of the issue
          example: "Bug"
        issue_priority:
          type: string
          description: Priority level of the issue
          example: "High"
        issue_priority_icon:
          type: string
          description: Icon identifier for the priority level
          example: "priority-high"
        status:
          type: string
          description: Current status of the issue
          example: "Done"
        issue_summary:
          type: string
          description: Brief summary of the issue
          example: "Fix authentication bug in login flow"
        assignee_name:
          type: string
          description: Name of the person assigned to the issue
          example: "John Doe"
        prs:
          type: integer
          description: Number of pull requests associated with this issue
          example: 2
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-15T10:30:00Z"
        statistic_value:
          type: number
          description: The calculated value for this specific issue
          example: 150.5
        formatted_statistic_value:
          type: string
          description: Human-readable formatted statistic value
          example: "2 hours 30 minutes"

    StatisticIssuesResponse:
      type: object
      description: Response containing issues for a specific statistic
      required:
        - statistic_type
        - statistic_name
        - total_issues
        - average_value
        - formatted_average_value
        - target_time
        - formatted_target_time
        - issues
      properties:
        statistic_type:
          type: string
          description: The statistic type slug
          example: "avg_time_for_code_review"
        statistic_name:
          type: string
          description: Human-readable name of the statistic
          example: "Average Time for Code Review"
        total_issues:
          type: integer
          description: Total number of issues that contributed to this statistic
          example: 25
        average_value:
          type: number
          description: Overall average value for this statistic
          example: 150.5
        formatted_average_value:
          type: string
          description: Human-readable formatted average value
          example: "2 hours 30 minutes"
        target_time:
          type: number
          description: Target time for this metric in minutes
          example: 240
        formatted_target_time:
          type: string
          description: Human-readable formatted target time
          example: "4 hours"
        issues:
          type: array
          items:
            $ref: "#/components/schemas/StatisticIssue"
          description: List of issues that contributed to this statistic

    Error:
      type: object
      description: Error response format
      required:
        - statusCode
        - message
      properties:
        statusCode:
          type: integer
          description: HTTP status code
          example: 400
        message:
          type: string
          description: Error message
          example: "Invalid date format"
        error:
          type: string
          description: Error type
          example: "Bad Request"

  responses:
    BadRequest:
      description: Bad request - invalid parameters or request format
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
          example:
            statusCode: 400
            message: "startDate must be before or equal to endDate"
            error: "Bad Request"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
          example:
            statusCode: 404
            message: "Issue with key GPS-9999 not found"
            error: "Not Found"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
          example:
            statusCode: 500
            message: "Internal server error"
            error: "Internal Server Error"

tags:
  - name: Health
    description: Health check and status endpoints
  - name: Issues
    description: Issue management and retrieval operations
  - name: Statistics
    description: Performance metrics and analytics
