/**
 * Represents a row returned from the status progress query.
 * Contains information about how much time an issue has spent in each status.
 */
export interface StatusProgressRow {
    /** Unique issue identifier (e.g., "GPS-5145") */
    issue: string;
    /** Status name (e.g., "To Do", "In Progress", "Code review", "Done") */
    status: string;
    /** Human-readable time spent in this status (e.g., "2 hours 30 minutes") */
    time_in_status: string;
}
