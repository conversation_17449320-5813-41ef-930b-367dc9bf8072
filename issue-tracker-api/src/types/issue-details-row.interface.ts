/**
 * Represents a row returned from the issue details query.
 * Contains detailed information about a specific issue from the Jira events table.
 */
export interface IssueDetailsRow {
    /** Type of the issue (e.g., "Task", "Bug", "Story") */
    issue_type: string;
    /** Unique issue identifier (e.g., "GPS-5145") */
    issue: string;
    /** Brief description/title of the issue */
    issue_summary: string;
    /** Name of the person assigned to the issue */
    assignee_name: string;
    /** Current status of the issue (e.g., "In Progress", "Code review", "Done") */
    status: string;
    /** Priority level of the issue (e.g., "Спешен", "Среден", "Нисък") */
    issue_priority: string;
    /** Icon identifier for the issue priority */
    issue_priority_icon: string;
}
