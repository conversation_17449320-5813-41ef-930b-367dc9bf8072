/**
 * Represents a change message within a timeline event.
 * Contains information about field changes made to an issue.
 */
export interface ChangeMessage {
    /** The field that was changed (e.g., "status", "assignee", "resolution") */
    field: string;
    /** The previous value of the field, or null if not applicable */
    from: string | null;
    /** The new value of the field, or null if not applicable */
    to: string | null;
    /** Name of the person who made the change */
    actor: string;
    /** Template message describing the change with placeholders */
    msg_template: string;
}

/**
 * Represents a row returned from the timeline events query.
 * Contains detailed information about events in an issue's timeline,
 * including Jira updates and Bitbucket activities.
 */
export interface TimelineRow {
    /** Type of event (e.g., "jira:issue_updated", "pullrequest:created") */
    event_type: string;
    /** Human-readable time elapsed since the previous event, or null for the first event */
    elapsed_time: string | null;
    /** Source system of the event ("jira" or "bitbucket") */
    source: string;
    /** Name of the person who triggered the event */
    actor_name: string;
    /** Repository name for Bitbucket events, may be empty for Jira events */
    repository: string;
    /** Pull request title for Bitbucket pull request events, null for other events */
    pr_title: string | null;
    /** Pull request link for Bitbucket pull request events, null for other events */
    pr_link: string | null;
    /** Pull request ID for Bitbucket pull request events, null for other events */
    pr_id: string | null;
    /** Pull request draft status for Bitbucket pull request events, null for other events */
    pr_is_draft: boolean | null;
    /** Indicates if the PR branch was closed (true=closed, false=active, null=non-PR events) */
    is_pr_branch_closed: boolean | null;
    /** Raw issue changelog data from the database for frontend processing */
    issue_changelog: any;
    /** Timestamp when the event occurred (ISO string format) */
    created_at: string;
}
