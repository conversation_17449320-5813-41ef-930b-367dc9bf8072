/**
 * Represents a row returned from the aggregated data query.
 * Contains JSON aggregated arrays of actors and events associated with an issue.
 */
export interface AggregatedDataRow {
    /** Array of unique actor names who have interacted with the issue, or null if none */
    actors: string[] | null;
    /** Array of unique event types that have occurred for the issue, or null if none */
    events: string[] | null;
}
