/**
 * Represents a row returned from database queries that calculate statistics.
 * Used for development and QA metrics such as average time in code review,
 * average time for bug QA to todo transitions, and average testing time.
 */
export interface StatisticRow {
    /** List of issues that contributed to this statistic calculation */
    issues: string[];
    /** Total number of issues included in the statistic calculation */
    total_issues: number;
    /** Human-readable formatted value of the statistic (e.g., "2 hours 30 minutes") */
    value: string;
    /** Target time for this metric in human-readable format */
    target_time: string;
    /** Type identifier for the statistic (e.g., 'avg_time_for_code_review') */
    stat_type: string;
}
