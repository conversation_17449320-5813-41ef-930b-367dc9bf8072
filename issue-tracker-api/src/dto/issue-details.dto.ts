export class IssueDetailsDto {
    issue_key: string;
    issue_type: string;
    issue_summary: string;
    assignee_name: string;
    current_status: string;
    issue_priority: string;
    issue_priority_icon: string;
}

export class ChangeMessageDto {
    field: string;
    from: string | null;
    to: string | null;
    actor: string;
    msg_template: string;
}

export class TimelineEventDto {
    event_type: string;
    elapsed_time: string | null;
    source: string;
    actor_name: string;
    repository: string;
    pr_title: string | null;
    pr_link: string | null;
    pr_id: string | null;
    pr_is_draft: boolean | null;
    is_pr_branch_closed: boolean | null;
    issue_changelog: any;
    created_at: string;
}

export class StatusProgressDto {
    status: string;
    time_in_status: string;
}

export class StatisticDto {
    total_issues: number;
    value: string;
    target_time: string;
    stat_type: string;
}

export class GroupedStatisticsDto {
    dev: StatisticDto[];
    qa: StatisticDto[];
}

export class FiltersDto {
    actors: string[];
    events: string[];
}

export class IssueDetailsResponseDto {
    issue_details: IssueDetailsDto;
    timeline: TimelineEventDto[];
    status_progress: StatusProgressDto[];
    filters: FiltersDto;
}

export class StatisticIssueDto {
    issue: string;
    issue_type: string;
    issue_priority: string;
    issue_priority_icon: string;
    status: string;
    issue_summary: string;
    assignee_name: string;
    prs: number;
    updated_at: string;
    /** The calculated value for this specific issue that contributed to the statistic */
    statistic_value: number;
    /** Human-readable formatted statistic value (e.g., "2 hours 30 minutes") */
    formatted_statistic_value: string;
}

export class StatisticIssuesResponseDto {
    /** The statistic type slug */
    statistic_type: string;
    /** Human-readable name of the statistic */
    statistic_name: string;
    /** Total number of issues that contributed to this statistic */
    total_issues: number;
    /** Overall average value for this statistic */
    average_value: number;
    /** Human-readable formatted average value */
    formatted_average_value: string;
    /** Target time for this metric */
    target_time: number;
    /** Human-readable formatted target time */
    formatted_target_time: string;
    /** List of issues that contributed to this statistic calculation */
    issues: StatisticIssueDto[];
}
