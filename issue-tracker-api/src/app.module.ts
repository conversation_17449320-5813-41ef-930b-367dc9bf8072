import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { IssuesController } from "./controllers/issues.controller";
import { DatabaseService } from "./services/database.service";

@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
            envFilePath: ".env",
        }),
    ],
    controllers: [AppController, IssuesController],
    providers: [AppService, DatabaseService],
})
export class AppModule {}
