version: "3.7"

services:
    app:
        image: ${DOCKERHUB_REPO_NAME}-fe:${IMAGE_TAG_NAME}
        container_name: ${CONTAINER_NAME}-fe
        environment:
            - API_URL
        ports:
            - ${EXTERNAL_PORT:-80}:80
        restart: always
        healthcheck:
            test: "exit 0"
        networks:
            - geoscan-net
    app-api:
        image: ${DOCKERHUB_REPO_NAME}-api:${IMAGE_TAG_NAME}
        container_name: ${CONTAINER_NAME}-api
        environment:
            - API_URL
            - DB_HOST
            - DB_PORT
            - DB_NAME
            - DB_USER
            - DB_PASSWORD
            - NODE_ENV
        ports:
            - ${EXTERNAL_API_PORT:-80}:3000
        restart: always
        healthcheck:
            test: "exit 0"
        networks:
            - geoscan-net
networks:
    geoscan-net:
        external: true
