---
- hosts: "servers"
  environment:
    EXTERNAL_PORT: "{{ lookup('env', 'EXTERNAL_PORT') }}"
    EXTERNAL_API_PORT: "{{ lookup('env', 'EXTERNAL_API_PORT') }}"
    API_URL: "{{ lookup('env', 'API_URL') }}"
    IMAGE_TAG_NAME: "{{ lookup('env', 'IMAGE_TAG_NAME') }}"
    DOCKERHUB_REPO_NAME: "{{ lookup('env', 'DOCKERHUB_REPO_NAME') }}"
    CONTAINER_NAME: "{{ lookup('env', 'CONTAINER_NAME') }}"
    DB_HOST: "{{ lookup('env', 'DB_HOST') }}"
    DB_PORT: "{{ lookup('env', 'DB_PORT') }}"
    DB_NAME: "{{ lookup('env', 'DB_NAME') }}"
    DB_USER: "{{ lookup('env', 'DB_USER') }}"
    DB_PASSWORD: "{{ lookup('env', 'DB_PASSWORD') }}"
    NODE_ENV: "{{ lookup('env', 'NODE_ENV') }}"

  tasks:
    - name: Log in
      community.docker.docker_login:
        username: "{{ lookup('env', 'DOCKERHUB_USER') }}"
        password: "{{ lookup('env', 'DOCKERHUB_PASSWORD') }}"

    - name: Copy docker-compose.prod.yml
      copy:
        src: docker-compose.prod.yml
        dest: ~/docker-compose.prod.yml
        mode: 0644

    - name: UP containers
      community.docker.docker_compose_v2:
        project_src: ~/
        project_name: "{{ lookup('env', 'CONTAINER_NAME') }}"
        recreate: always
        pull: always
        files:
          - docker-compose.prod.yml

    - name: Image Prune
      community.docker.docker_prune:
        images: yes

    - name: Delete docker-compose.prod.yml
      ansible.builtin.file:
        path: ~/docker-compose.prod.yml
        state: absent
