image: atlassian/default-image:4

options:
    docker: true

definitions:
    services:
        docker:
            memory: 7168
    steps:
        - step: &build
              name: "Build"
              size: 2x
              services:
                  - docker
              script:
                  - export ANSIBLE_HOST_KEY_CHECKING="False"
                  - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                  - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                  - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                  - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                  - export CONTAINER_NAME=$CONTAINER_NAME
                  - export EXTERNAL_PORT=$EXTERNAL_PORT
                  - export EXTERNAL_API_PORT=$EXTERNAL_API_PORT
                  - export API_URL=$API_URL
                  - export DOCKER_BUILDKIT=0
                  - echo "Building the image and push to docker hub registry"
                  - docker login -u $DOCKERHUB_USER -p $DOCKERHUB_PASSWORD
                  - docker build -f issue-tracker/.docker/Dockerfile -t ${DOCKERHUB_REPO_NAME}-fe:${IMAGE_TAG_NAME} --no-cache issue-tracker/
                  - docker build -f issue-tracker-api/.docker/Dockerfile -t ${DOCKERHUB_REPO_NAME}-api:${IMAGE_TAG_NAME} --no-cache issue-tracker-api/
                  - docker push ${DOCKERHUB_REPO_NAME}-fe:${IMAGE_TAG_NAME}
                  - docker push ${DOCKERHUB_REPO_NAME}-api:${IMAGE_TAG_NAME}
                  - docker logout
        - step: &deploy
              name: "Deploy"
              size: 2x
              runs-on:
                  - "self.hosted"
                  - "linux"
                  - "gsui"
                  - "build"
              image: willhallonline/ansible:2.16-alpine-3.19
              deployment: staging
              services:
                  - docker
              script:
                  - export ANSIBLE_HOST_KEY_CHECKING="False"
                  - export ANSIBLE_ENABLE_TASK_DEBUGGER=$ANSIBLE_ENABLE_TASK_DEBUGGER
                  - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                  - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                  - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                  - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                  - export CONTAINER_NAME=$CONTAINER_NAME
                  - export EXTERNAL_PORT=$EXTERNAL_PORT
                  - export EXTERNAL_API_PORT=$EXTERNAL_API_PORT
                  - ansible-galaxy collection install community.docker
                  - ansible-playbook -i .ansible/hosts ansible-deploy.yml -l staging
pipelines:
    branches:
        "master":
            - step: *build
            - step: *deploy
    custom:
        "Staging":
            - step: *build
            - step:
                  <<: *deploy
                  deployment: staging
